{"name": "码上通", "appid": "__UNI__FA08E49", "description": "", "versionName": "1.1.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wxccd7e2a0911b3397", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true}, "vueVersion": "2", "h5": {"devServer": {"port": 8080, "https": true, "disableHostCheck": true, "proxy": {"/dev-api": {"target": "http://***********:9876", "changeOrigin": true, "pathRewrite": {"^/dev-api": ""}}, "/upload": {"target": "http://***********:9876/common/upload", "changeOrigin": true, "pathRewrite": {"^/upload": ""}}}}, "title": "码上通", "router": {"mode": "hash", "base": "./"}, "template": "static/template.h5.html", "optimization": {"treeShaking": {"enable": true}}, "async": {"loading": "AsyncLoading", "error": "AsyncError", "delay": 200, "timeout": 3000}}}